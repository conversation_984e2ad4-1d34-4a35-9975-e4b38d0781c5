package com.wzsec.webproxy.controller;

import com.wzsec.webproxy.watermark.util.WatermarkExtractor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 水印检测API控制器
 * 提供唯一的水印检测接口
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
@Slf4j
@RestController
@RequestMapping("/api/watermark")
public class WatermarkDetectionController {

    @Autowired
    private WatermarkExtractor watermarkExtractor;

    /**
     * 检测内容是否包含水印
     * 直接接收原始内容，自动识别内容类型
     */
    @PostMapping("/detect")
    public ResponseEntity<Map<String, Object>> detectWatermark(
            @RequestBody String content,
            HttpServletRequest request) {

        Map<String, Object> response = new HashMap<>();

        try {
            // 自动识别内容类型
            String contentType = detectContentType(content, request);

            log.info("收到水印检测请求 - 内容长度: {}, 自动识别类型: {}, 来源IP: {}",
                    content.length(), contentType, request.getRemoteAddr());

            if (content == null || content.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "内容不能为空");
                return ResponseEntity.badRequest().body(response);
            }

            // 提取水印
            List<WatermarkExtractor.ExtractedWatermark> watermarks =
                watermarkExtractor.extractWatermarks(content, contentType);

            // 构建检测结果
            Map<String, Object> result = new HashMap<>();
            result.put("hasWatermark", !watermarks.isEmpty());
            result.put("watermarkCount", watermarks.size());
            result.put("contentLength", content.length());
            result.put("contentType", contentType);

            if (!watermarks.isEmpty()) {
                // 计算可信度分数
                double confidenceScore = calculateConfidenceScore(watermarks);
                result.put("confidenceScore", confidenceScore);

                // 提取基本信息
                WatermarkExtractor.ExtractedWatermark firstWatermark = watermarks.get(0);
                if (firstWatermark.getWatermarkInfo() != null) {
                    result.put("userId", firstWatermark.getWatermarkInfo().getUserId());
                    result.put("ipAddress", firstWatermark.getWatermarkInfo().getIpAddress());
                    result.put("timestamp", firstWatermark.getWatermarkInfo().getTimestamp());
                }

                result.put("riskLevel", assessRiskLevel(watermarks.size(), confidenceScore));
            } else {
                result.put("confidenceScore", 0.0);
                result.put("riskLevel", "NONE");
            }

            response.put("success", true);
            response.put("data", result);
            response.put("message", watermarks.isEmpty() ?
                "未检测到水印" : String.format("检测到 %d 个水印", watermarks.size()));

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("水印检测失败", e);
            response.put("success", false);
            response.put("message", "检测失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 计算可信度分数
     */
    private double calculateConfidenceScore(List<WatermarkExtractor.ExtractedWatermark> watermarks) {
        if (watermarks.isEmpty()) {
            return 0.0;
        }

        double baseScore = 0.3; // 基础分数
        double countBonus = Math.min(watermarks.size() * 0.2, 0.5); // 数量加成，最多0.5
        double validityBonus = 0.0;

        // 检查水印有效性
        int validWatermarks = 0;
        for (WatermarkExtractor.ExtractedWatermark watermark : watermarks) {
            if (watermark.getWatermarkInfo() != null &&
                watermark.getWatermarkInfo().getUserId() != null &&
                !watermark.getWatermarkInfo().getUserId().trim().isEmpty()) {
                validWatermarks = 1;
                validityBonus += 0.1;
            }
        }

        if (validWatermarks > 0) {
            validityBonus += 0.1; // 有效水印额外加成
        }

        return Math.min(baseScore + countBonus + validityBonus, 1.0);
    }

    /**
     * 评估风险等级
     */
    private String assessRiskLevel(int watermarkCount, double confidenceScore) {
        if (watermarkCount == 0 || confidenceScore < 0.3) {
            return "NONE";
        } else if (watermarkCount <= 2 && confidenceScore < 0.6) {
            return "LOW";
        } else if (watermarkCount <= 5 && confidenceScore < 0.8) {
            return "MEDIUM";
        } else {
            return "HIGH";
        }
    }

    /**
     * 自动检测内容类型
     */
    private String detectContentType(String content, HttpServletRequest request) {
        // 1. 首先检查请求头中的Content-Type
        String requestContentType = request.getContentType();
        if (requestContentType != null && !requestContentType.isEmpty()) {
            // 移除charset等参数，只保留主要类型
            String mainType = requestContentType.split(";")[0].trim().toLowerCase();
            if (isSupportedContentType(mainType)) {
                log.debug("从请求头识别内容类型: {}", mainType);
                return mainType;
            }
        }

        // 2. 基于内容特征自动识别
        String detectedType = detectContentTypeByContent(content);
        log.debug("基于内容特征识别类型: {}", detectedType);
        return detectedType;
    }

    /**
     * 基于内容特征检测类型
     */
    private String detectContentTypeByContent(String content) {
        if (content == null || content.trim().isEmpty()) {
            return "text/plain";
        }

        String trimmedContent = content.trim();

        // 检测JSON格式
        if (isJsonContent(trimmedContent)) {
            return "application/json";
        }

        // 检测XML格式
        if (isXmlContent(trimmedContent)) {
            return "application/xml";
        }

        // 检测HTML格式
        if (isHtmlContent(trimmedContent)) {
            return "text/html";
        }

        // 默认为纯文本
        return "text/plain";
    }

    /**
     * 检测是否为JSON内容
     */
    private boolean isJsonContent(String content) {
        try {
            // 简单的JSON格式检测
            return (content.startsWith("{") && content.endsWith("}")) ||
                   (content.startsWith("[") && content.endsWith("]"));
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检测是否为XML内容
     */
    private boolean isXmlContent(String content) {
        return content.startsWith("<?xml") ||
               (content.startsWith("<") && content.endsWith(">") && content.contains("</"));
    }

    /**
     * 检测是否为HTML内容
     */
    private boolean isHtmlContent(String content) {
        String lowerContent = content.toLowerCase();
        return lowerContent.contains("<!doctype html") ||
               lowerContent.contains("<html") ||
               lowerContent.contains("<head>") ||
               lowerContent.contains("<body>");
    }

    /**
     * 检查是否为支持的内容类型
     */
    private boolean isSupportedContentType(String contentType) {
        return "application/json".equals(contentType) ||
               "application/xml".equals(contentType) ||
               "text/xml".equals(contentType) ||
               "text/html".equals(contentType) ||
               "text/plain".equals(contentType);
    }
}
