# 水印检测API文档

## 概述

dwts-web-proxy 现在只提供一个核心的水印检测接口，用于检测内容中是否包含水印信息。

## API接口

### 检测水印

**接口地址：** `POST /api/watermark/detect`

**功能描述：** 检测提供的内容中是否包含水印，并返回详细的检测结果。支持自动识别内容类型。

#### 请求方式

直接将待检测的内容作为请求体发送，无需JSON包装。系统会自动识别内容类型。

#### 内容类型识别

系统会按以下优先级自动识别内容类型：
1. **请求头识别**：优先使用请求头中的 `Content-Type`
2. **内容特征识别**：基于内容格式特征自动判断
   - JSON：以 `{` 或 `[` 开头并相应结尾
   - XML：以 `<?xml` 开头或包含XML标签结构
   - HTML：包含HTML文档特征（如 `<!doctype html`、`<html>`、`<head>`、`<body>` 等）
   - 纯文本：默认类型

#### 请求示例

**检测JSON内容：**
```bash
curl -X POST "http://localhost:9090/api/watermark/detect" \
  -H "Content-Type: application/json" \
  -d '{"id":12,"label":"202303071440"}'
```

**检测纯文本内容：**
```bash
curl -X POST "http://localhost:9090/api/watermark/detect" \
  -H "Content-Type: text/plain" \
  -d "这里是待检测的文本内容"
```

**自动识别内容类型：**
```bash
# 系统会自动识别为JSON格式
curl -X POST "http://localhost:9090/api/watermark/detect" \
  -d '{"data": "test content"}'
```

#### 响应格式

```json
{
  "success": true,
  "data": {
    "hasWatermark": true,
    "watermarkCount": 1,
    "contentLength": 256,
    "contentType": "application/json",
    "confidenceScore": 0.85,
    "userId": "user123",
    "ipAddress": "*************",
    "timestamp": 1672531200000,
    "riskLevel": "MEDIUM"
  },
  "message": "检测到 1 个水印"
}
```

#### 响应字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| success | Boolean | 请求是否成功 |
| data | Object | 检测结果数据 |
| data.hasWatermark | Boolean | 是否包含水印 |
| data.watermarkCount | Integer | 检测到的水印数量 |
| data.contentLength | Integer | 内容长度 |
| data.contentType | String | 自动识别的内容类型 |
| data.confidenceScore | Double | 可信度分数（0.0-1.0） |
| data.userId | String | 水印中的用户ID（如果有） |
| data.ipAddress | String | 水印中的IP地址（如果有） |
| data.timestamp | Long | 水印时间戳（如果有） |
| data.riskLevel | String | 风险等级（NONE/LOW/MEDIUM/HIGH） |
| message | String | 响应消息 |

#### 风险等级说明

- **NONE**: 未检测到水印或可信度极低
- **LOW**: 检测到少量水印，可信度较低
- **MEDIUM**: 检测到中等数量水印，可信度中等
- **HIGH**: 检测到大量水印，可信度高

#### 错误响应

```json
{
  "success": false,
  "message": "内容不能为空"
}
```

## 支持的内容类型

- `text/plain` - 纯文本
- `application/json` - JSON格式
- `application/xml` - XML格式
- `text/html` - HTML格式

## 使用示例

### 检测JSON内容（指定Content-Type）

```bash
curl -X POST "http://localhost:9090/api/watermark/detect" \
  -H "Content-Type: application/json" \
  -d '{"id":12,"label":"202303071440"}'
```

### 检测纯文本（指定Content-Type）

```bash
curl -X POST "http://localhost:9090/api/watermark/detect" \
  -H "Content-Type: text/plain" \
  -d "这是一段包含水印的文本内容"
```

### 自动识别内容类型

```bash
# JSON格式 - 系统自动识别
curl -X POST "http://localhost:9090/api/watermark/detect" \
  -d '[{"id":12,"label":"test"}]'

# XML格式 - 系统自动识别
curl -X POST "http://localhost:9090/api/watermark/detect" \
  -d '<?xml version="1.0"?><root><item>test</item></root>'

# HTML格式 - 系统自动识别
curl -X POST "http://localhost:9090/api/watermark/detect" \
  -d '<html><head><title>Test</title></head><body>Content</body></html>'
```

## 注意事项

1. **内容要求**：内容不能为空或仅包含空白字符
2. **内容类型识别**：
   - 优先使用请求头中的 `Content-Type`
   - 如果请求头未指定或不支持，系统会基于内容特征自动识别
   - 支持的类型：`application/json`、`application/xml`、`text/xml`、`text/html`、`text/plain`
3. **检测算法**：检测结果的可信度分数基于多个因素计算，包括水印数量和有效性
4. **风险评估**：风险等级评估基于水印数量和可信度分数
5. **信息提取**：如果检测到水印，会尝试提取其中的用户信息（用户ID、IP地址、时间戳等）
6. **请求格式**：直接发送原始内容，无需JSON包装，简化了接口调用
